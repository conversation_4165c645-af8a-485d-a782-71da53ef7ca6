const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = multer();
router.post('/image', upload.single('photo'), async (req,res)=>{
  try{
    // Placeholder detection - replace with real model
    res.json({disease:'No disease detected (demo)', confidence:0.98, remedy:'Healthy crop. Maintain watering schedule.'});
  }catch(err){
    console.error(err);
    res.status(500).json({error:'Detection failed'});
  }
});
module.exports = router;
