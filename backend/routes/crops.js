const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const CropRecord = require('../models/CropRecord');
function auth(req,res,next){
  const header = req.headers.authorization;
  if(!header) return res.status(401).json({error:'No token'});
  const token = header.split(' ')[1];
  try{
    req.user = jwt.verify(token, process.env.JWT_SECRET || 'devsecret');
    next();
  }catch(e){
    res.status(401).json({error:'Invalid token'});
  }
}
const growthTable = { Maize: [90,120], Wheat: [100,140], Rice: [120,160], Potato: [80,110], Tomato: [70,110] };
router.post('/add', auth, async (req,res)=>{
  const {crop, plantedOn, areaHectares, notes} = req.body;
  if(!crop||!plantedOn) return res.status(400).json({error:'Missing fields'});
  const record = new CropRecord({userId:req.user.id, crop, plantedOn, areaHectares, notes});
  await record.save();
  res.json({record});
});
router.get('/list', auth, async (req,res)=>{
  const list = await CropRecord.find({userId:req.user.id}).sort({createdAt:-1});
  res.json({list});
});
router.get('/expect/:id', auth, async (req,res)=>{
  const rec = await CropRecord.findById(req.params.id);
  if(!rec) return res.status(404).json({error:'Not found'});
  const crop = rec.crop;
  const [minD, maxD] = growthTable[crop] || [90,120];
  const planted = new Date(rec.plantedOn);
  const from = new Date(planted); from.setDate(planted.getDate()+minD);
  const to = new Date(planted); to.setDate(planted.getDate()+maxD);
  res.json({expectedFrom: from.toISOString().slice(0,10), expectedTo: to.toISOString().slice(0,10)});
});
module.exports = router;
