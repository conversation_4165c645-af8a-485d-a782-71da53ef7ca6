const express = require('express');
const router = express.Router();
router.post('/ask', async (req,res)=>{
  try{
    const {message} = req.body;
    // Demo reply - replace with OpenAI integration if you have an API key
    const reply = `Demo AgriAI: I received your question: "${message}". Give simple, local-language friendly advice.`;
    res.json({reply});
  }catch(err){
    console.error(err);
    res.status(500).json({error:'Chat failed'});
  }
});
module.exports = router;
