const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
router.post('/register', async (req,res)=>{
  try{
    const {name, phone, password, lat, lon} = req.body;
    if(!name||!phone||!password) return res.status(400).json({error:'Missing fields'});
    const existing = await User.findOne({phone});
    if(existing) return res.status(400).json({error:'Phone already registered'});
    const hash = await bcrypt.hash(password, 10);
    const user = new User({name, phone, passwordHash:hash, location:{lat, lon}});
    await user.save();
    const token = jwt.sign({id:user._id}, process.env.JWT_SECRET || 'devsecret');
    res.json({token, user:{id:user._id, name:user.name, phone:user.phone}});
  }catch(err){
    console.error(err);
    res.status(500).json({error:'Server error'});
  }
});
router.post('/login', async (req,res)=>{
  try{
    const {phone, password} = req.body;
    const user = await User.findOne({phone});
    if(!user) return res.status(401).json({error:'Invalid credentials'});
    const ok = await bcrypt.compare(password, user.passwordHash);
    if(!ok) return res.status(401).json({error:'Invalid credentials'});
    const token = jwt.sign({id:user._id}, process.env.JWT_SECRET || 'devsecret');
    res.json({token, user:{id:user._id, name:user.name, phone:user.phone}});
  }catch(err){
    res.status(500).json({error:'Server error'});
  }
});
module.exports = router;
