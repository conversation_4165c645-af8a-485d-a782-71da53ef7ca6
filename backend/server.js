require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const authRoutes = require('./routes/auth');
const cropsRoutes = require('./routes/crops');
const detectRoutes = require('./routes/detect');
const chatRoutes = require('./routes/chat');
const app = express();
app.use(cors());
app.use(express.json({limit:'10mb'}));
const PORT = process.env.PORT || 4000;
mongoose.connect(process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/agri', {useNewUrlParser:true, useUnifiedTopology:true})
  .then(()=>console.log('Mongo connected'))
  .catch(err=>console.error('Mongo error', err));
app.use('/api/auth', authRoutes);
app.use('/api/crops', cropsRoutes);
app.use('/api/detect', detectRoutes);
app.use('/api/chat', chatRoutes);
app.get('/', (req,res)=>res.send('AgriAI Backend running'));
app.listen(PORT, ()=>console.log(`Server running on ${PORT}`));
