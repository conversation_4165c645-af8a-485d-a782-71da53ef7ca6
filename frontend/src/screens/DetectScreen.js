import React, {useState} from 'react';
import {View, Text, Button, Alert} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as api from '../services/api';
export default function DetectScreen(){
  const [status, setStatus] = useState('');
  async function pickImage(){
    const perm = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if(!perm.granted) return alert('Permission required');
    const res = await ImagePicker.launchImageLibraryAsync({base64:false, quality:0.7});
    if(res.canceled) return;
    const asset = res.assets?.[0] || res;
    const uri = asset.uri;
    const formData = new FormData();
    const filename = uri.split('/').pop();
    const match = /\.(\w+)$/.exec(filename);
    const type = match ? `image/${match[1]}` : 'image';
    formData.append('photo', { uri, name: filename, type });
    setStatus('Uploading...');
    try{
      const out = await api.detectImage(formData);
      setStatus(`Result: ${out.disease} (conf ${out.confidence})\n${out.remedy}`);
    }catch(err){
      Alert.alert('Detect failed', err.message || JSON.stringify(err));
      setStatus('');
    }
  }
  return (
    <View style={{flex:1, padding:20}}>
      <Text style={{fontSize:18}}>Crop Disease Detector</Text>
      <View style={{height:12}} />
      <Button title="Pick a photo" onPress={pickImage} />
      <View style={{height:12}} />
      <Text>{status}</Text>
    </View>
  );
}
