import React, {useState} from 'react';
import {View, Text, TextInput, Button, Alert} from 'react-native';
import * as api from '../services/api';
export default function RegisterScreen({navigation}){
  const [name,setName]=useState('');
  const [phone,setPhone]=useState('');
  const [password,setPassword]=useState('');
  async function doRegister(){
    try{
      const data = await api.register({name, phone, password});
      api.setToken(data.token);
      navigation.replace('Dashboard', {user:data.user});
    }catch(err){
      Alert.alert('Register failed', err.response?.data?.error || err.message);
    }
  }
  return (
    <View style={{flex:1, padding:20, justifyContent:'center'}}>
      <Text style={{fontSize:22, marginBottom:12}}>Register</Text>
      <Text>Name</Text>
      <TextInput value={name} onChangeText={setName} style={{borderWidth:1, padding:8, marginBottom:12}} />
      <Text>Phone</Text>
      <TextInput value={phone} onChangeText={setPhone} style={{borderWidth:1, padding:8, marginBottom:12}} />
      <Text>Password</Text>
      <TextInput value={password} onChangeText={setPassword} secureTextEntry style={{borderWidth:1, padding:8, marginBottom:12}} />
      <Button title="Create account" onPress={doRegister} />
    </View>
  );
}
