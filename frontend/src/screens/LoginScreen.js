import React, {useState} from 'react';
import {View, Text, TextInput, Button, Alert} from 'react-native';
import * as api from '../services/api';
export default function LoginScreen({navigation}){
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  async function doLogin(){
    try{
      const data = await api.login({phone, password});
      api.setToken(data.token);
      navigation.replace('Dashboard', {user:data.user});
    }catch(err){
      Alert.alert('Login failed', err.response?.data?.error || err.message);
    }
  }
  return (
    <View style={{flex:1, padding:20, justifyContent:'center'}}>
      <Text style={{fontSize:24, marginBottom:20}}>AgriAI - Login</Text>
      <Text>Phone</Text>
      <TextInput value={phone} onChangeText={setPhone} style={{borderWidth:1, padding:8, marginBottom:12}} />
      <Text>Password</Text>
      <TextInput value={password} onChangeText={setPassword} secureTextEntry style={{borderWidth:1, padding:8, marginBottom:12}} />
      <Button title="Login" onPress={doLogin} />
      <View style={{height:12}} />
      <Button title="Register" onPress={()=>navigation.navigate('Register')} />
    </View>
  );
}
