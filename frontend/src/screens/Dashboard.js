import React, {useState, useEffect} from 'react';
import {View, Text, Button, FlatList, TouchableOpacity, Alert} from 'react-native';
import * as api from '../services/api';
export default function Dashboard({navigation, route}){
  const user = route.params?.user;
  const [crops, setCrops] = useState([]);
  async function load(){ const res = await api.listCrops(); setCrops(res.list); }
  useEffect(()=>{load()}, []);
  return (
    <View style={{flex:1, padding:16}}>
      <Text style={{fontSize:20}}>Hello, {user?.name}</Text>
      <View style={{height:12}} />
      <Button title="Add Crop (demo)" onPress={async ()=>{ await api.addCrop({crop:'Maize', plantedOn: new Date().toISOString(), areaHectares:0.5}); load(); }} />
      <View style={{height:12}} />
      <Button title="Detect Disease" onPress={()=>navigation.navigate('Detect')} />
      <View style={{height:12}} />
      <Button title="Chat with AI" onPress={()=>navigation.navigate('Chat')} />
      <View style={{height:20}} />
      <Text style={{fontSize:16, marginBottom:8}}>Your crops</Text>
      <FlatList data={crops} keyExtractor={i=>i._id} renderItem={({item})=> (
        <TouchableOpacity onPress={async ()=>{ const win = await api.expectWindow(item._id); Alert.alert('Harvest window', `${win.expectedFrom} → ${win.expectedTo}`); }} style={{padding:12, borderWidth:1, marginBottom:8}}>
          <Text style={{fontWeight:'bold'}}>{item.crop}</Text>
          <Text>Planted on: {new Date(item.plantedOn).toISOString().slice(0,10)}</Text>
        </TouchableOpacity>
      )} />
    </View>
  );
}
