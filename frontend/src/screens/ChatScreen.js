import React, {useState} from 'react';
import {View, TextInput, Button, Text, ScrollView} from 'react-native';
import * as api from '../services/api';
export default function ChatScreen(){
  const [msg, setMsg] = useState('');
  const [history, setHistory] = useState([]);
  async function send(){
    if(!msg) return;
    setHistory(h=>[...h, {from:'user', text:msg}]);
    setMsg('');
    const res = await api.askChat(msg);
    setHistory(h=>[...h, {from:'ai', text:res.reply}]);
  }
  return (
    <View style={{flex:1, padding:12}}>
      <ScrollView style={{flex:1, marginBottom:12}}>
        {history.map((m,i)=> (
          <Text key={i} style={{marginVertical:6, backgroundColor:m.from==='ai'? '#eef':'#efe', padding:8, borderRadius:6}}>{m.text}</Text>
        ))}
      </ScrollView>
      <TextInput value={msg} onChangeText={setMsg} placeholder="Ask (e.g. 'Why are maize leaves yellow?')" style={{borderWidth:1, padding:8, marginBottom:8}} />
      <Button title="Send" onPress={send} />
    </View>
  );
}
