import axios from 'axios';
const API_BASE = 'http://10.57.23.190:4000/api';
let token = null;
export function setToken(t){ token = t; }
export async function register(data){ return axios.post(`${API_BASE}/auth/register`, data).then(r=>r.data); }
export async function login(data){ return axios.post(`${API_BASE}/auth/login`, data).then(r=>r.data); }
export async function addCrop(data){ return axios.post(`${API_BASE}/crops/add`, data, {headers:{Authorization:`Bearer ${token}`}}).then(r=>r.data); }
export async function listCrops(){ return axios.get(`${API_BASE}/crops/list`, {headers:{Authorization:`Bearer ${token}`}}).then(r=>r.data); }
export async function expectWindow(id){ return axios.get(`${API_BASE}/crops/expect/${id}`, {headers:{Authorization:`Bearer ${token}`}}).then(r=>r.data); }
export async function detectImage(formData){ return axios.post(`${API_BASE}/detect/image`, formData, {headers:{'Content-Type':'multipart/form-data', Authorization:`Bearer ${token}`}}).then(r=>r.data); }
export async function askChat(msg){ return axios.post(`${API_BASE}/chat/ask`, {message:msg}).then(r=>r.data); }
