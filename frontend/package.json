{"name": "agri-frontend", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios"}, "dependencies": {"expo": "~48.0.0", "expo-status-bar": "~1.4.0", "react": "18.2.0", "react-native": "0.71.8", "axios": "^1.4.0", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "expo-image-picker": "^14.0.1"}}