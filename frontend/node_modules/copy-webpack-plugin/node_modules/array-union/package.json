{"name": "array-union", "version": "3.0.1", "description": "Create an array of unique values, in order, from the input arrays", "license": "MIT", "repository": "sindresorhus/array-union", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["array", "set", "uniq", "unique", "duplicate", "remove", "union", "combine", "merge"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "tsd": {"compilerOptions": {"esModuleInterop": true}}}