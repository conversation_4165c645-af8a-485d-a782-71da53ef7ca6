{"version": 3, "file": "sharp.js", "sourceRoot": "", "sources": ["../src/sharp.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAA2C;AAC3C,gDAAwB;AACxB,gEAAuC;AACvC,oDAA4B;AAE5B,+BAA4B;AAG5B,MAAM,kBAAkB,GAAG,2CAA2C,CAAC;AACvE,MAAM,sBAAsB,GAAG,QAAQ,CAAC;AAEjC,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IACrE,MAAM,KAAK,GAAG,MAAM,sBAAsB,EAAE,CAAC;IAE7C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAChD,8BAA8B;IAC9B,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,GAAG,CACtC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QACpB,MAAM,OAAO,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;QAC3F,OAAO,KAAK,CAAC,MAAM,EAAE;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;SACzD,CAAC;aACC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;aAC3E,QAAQ,EAAE,CAAC;IAChB,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,cAAc,CAAC;AACxB,CAAC;AAjBD,8CAiBC;AAED;;;GAGG;AACI,KAAK,UAAU,gBAAgB;IACpC,IAAI,SAAG,CAAC,yBAAyB,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IACD,IAAI;QACF,OAAO,CAAC,CAAC,CAAC,MAAM,iBAAiB,EAAE,CAAC,CAAC;KACtC;IAAC,MAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AATD,4CASC;AAEM,KAAK,UAAU,UAAU,CAC9B,OAA2B,EAC3B,WAAkC,EAAE;IAEpC,MAAM,GAAG,GAAG,MAAM,iBAAiB,EAAE,CAAC;IACtC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EAAC,GAAG,EAAE;YACvC,GAAG,UAAU,CAAC,OAAO,CAAC;YACtB,GAAG,iBAAiB,CAAC,QAAQ,CAAC;SAC/B,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,eAAe,CAAC;KACxB;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CACb,8CAA8C;gBAC5C,KAAK,CAAC,OAAO;gBACb,YAAY;gBACZ,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAC/C,CAAC;SACH;aAAM;YACL,MAAM,KAAK,CAAC;SACb;KACF;AACH,CAAC;AAxBD,gCAwBC;AAED,SAAS,UAAU,CAAC,OAAgB;IAClC,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;YACpC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;aACvB;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACxC;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;aAC9B;SACF;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,CAAC,QAA+B;IACxD,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,IAAI,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE;YAClC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;SACpE;aAAM;YACL,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAI,SAAS,GAAkB,IAAI,CAAC;AACpC,IAAI,cAAc,GAAe,IAAI,CAAC;AAEtC,KAAK,UAAU,iBAAiB;IAC9B,IAAI,SAAS,EAAE;QACb,OAAO,SAAS,CAAC;KAClB;IACD,IAAI;QACF,MAAM,eAAe,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtD,IACE,eAAe;YACf,gBAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,sBAAsB,CAAC;YACjE,OAAO,eAAe,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ;YAC7C,OAAO,cAAc,KAAK,QAAQ,EAClC;YACA,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;YACtE,OAAO,SAAS,CAAC;SAClB;KACF;IAAC,MAAM;QACN,gCAAgC;KACjC;IAED,IAAI,mBAAmB,CAAC;IACxB,IAAI;QACF,mBAAmB,GAAG,CAAC,MAAM,IAAA,qBAAU,EAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;KAC3F;IAAC,MAAM;QACN,MAAM,aAAa,CAAC,sBAAsB,CAAC,CAAC;KAC7C;IAED,IAAI,CAAC,gBAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,EAAE;QAClE,0BAA0B,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;KACzE;IACD,SAAS,GAAG,OAAO,CAAC;IACpB,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,sBAAsB;IAC1C,IAAI,SAAG,CAAC,yBAAyB,EAAE;QACjC,MAAM,IAAI,KAAK,CACb,gJAAgJ,CACjJ,CAAC;KACH;IACD,IAAI,cAAc,EAAE;QAClB,OAAO,cAAc,CAAC;KACvB;IACD,sCAAsC;IACtC,MAAM,iBAAiB,EAAE,CAAC;IAE1B,qCAAqC;IACrC,IAAI;QACF,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,cAAc,GAAG,KAAK,CAAC;QACvB,OAAO,KAAK,CAAC;KACd;IAAC,MAAM,GAAE;IAEV,+DAA+D;IAC/D,IAAI,YAAY,CAAC;IACjB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChC,IAAI;YACF,YAAY,GAAG,CAAC,MAAM,IAAA,qBAAU,EAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;SAChF;QAAC,MAAM,GAAE;KACX;SAAM;QACL,qGAAqG;QACrG,2FAA2F;QAC3F,oDAAoD;QACpD,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI;YACF,cAAc,GAAG,cAAI,CAAC,IAAI,CACxB,CAAC,MAAM,IAAA,qBAAU,EAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EACtE,cAAc,CACf,CAAC;SACH;QAAC,MAAM,GAAE;QACV,IAAI;YACF,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,wBAAwB,EAAE;gBACvD,KAAK,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;aACzE,CAAC,CAAC;SACJ;QAAC,MAAM,GAAE;KACX;IAED,2CAA2C;IAC3C,MAAM,SAAS,GAAG,sBAAW,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IAElE,IAAI,SAAS,EAAE;QACb,IAAI;YACF,8CAA8C;YAC9C,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;SACrC;QAAC,MAAM,GAAE;KACX;IAED,IAAI,CAAC,cAAc,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AA1DD,wDA0DC;AAED,SAAS,aAAa,CAAC,kBAA0B;IAC/C,OAAO,IAAI,KAAK,CACd,iCAAiC,kBAAkB,uBAAuB;QACxE,uDAAuD,kBAAkB,QAAQ;QACjF,IAAI;QACJ,kFAAkF,CACrF,CAAC;AACJ,CAAC;AAED,IAAI,2BAA2B,GAAG,KAAK,CAAC;AAExC,SAAS,0BAA0B,CAAC,kBAA0B,EAAE,mBAA2B;IACzF,IAAI,2BAA2B,EAAE;QAC/B,OAAO;KACR;IACD,OAAO,CAAC,IAAI,CACV,0CAA0C,kBAAkB,uBAAuB;QACjF,iCAAiC,mBAAmB,MAAM;QAC1D,sBAAsB,kBAAkB,MAAM;QAC9C,uDAAuD,kBAAkB,QAAQ;QACjF,IAAI;QACJ,kFAAkF,CACrF,CAAC;IACF,2BAA2B,GAAG,IAAI,CAAC;AACrC,CAAC", "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport { env } from './env';\nimport { Options, SharpCommandOptions, SharpGlobalOptions } from './sharp.types';\n\nconst SHARP_HELP_PATTERN = /\\n\\nSpecify --help for available options/g;\nconst SHARP_REQUIRED_VERSION = '^2.1.0';\n\nexport async function resizeBufferAsync(buffer: Buffer, sizes: number[]): Promise<Buffer[]> {\n  const sharp = await findSharpInstanceAsync();\n\n  const metadata = await sharp(buffer).metadata();\n  // Create buffer for each size\n  const resizedBuffers = await Promise.all(\n    sizes.map(dimension => {\n      const density = (dimension / Math.max(metadata.width, metadata.height)) * metadata.density;\n      return sharp(buffer, {\n        density: isNaN(density) ? undefined : Math.ceil(density),\n      })\n        .resize(dimension, dimension, { fit: 'contain', background: 'transparent' })\n        .toBuffer();\n    })\n  );\n\n  return resizedBuffers;\n}\n\n/**\n * Returns `true` if a global sharp instance can be found.\n * This functionality can be overridden with `process.env.EXPO_IMAGE_UTILS_NO_SHARP=1`.\n */\nexport async function isAvailableAsync(): Promise<boolean> {\n  if (env.EXPO_IMAGE_UTILS_NO_SHARP) {\n    return false;\n  }\n  try {\n    return !!(await findSharpBinAsync());\n  } catch {\n    return false;\n  }\n}\n\nexport async function sharpAsync(\n  options: SharpGlobalOptions,\n  commands: SharpCommandOptions[] = []\n): Promise<string[]> {\n  const bin = await findSharpBinAsync();\n  try {\n    const { stdout } = await spawnAsync(bin, [\n      ...getOptions(options),\n      ...getCommandOptions(commands),\n    ]);\n    const outputFilePaths = stdout.trim().split('\\n');\n    return outputFilePaths;\n  } catch (error: any) {\n    if (error.stderr) {\n      throw new Error(\n        '\\nProcessing images using sharp-cli failed: ' +\n          error.message +\n          '\\nOutput: ' +\n          error.stderr.replace(SHARP_HELP_PATTERN, '')\n      );\n    } else {\n      throw error;\n    }\n  }\n}\n\nfunction getOptions(options: Options): string[] {\n  const args = [];\n  for (const [key, value] of Object.entries(options)) {\n    if (value != null && value !== false) {\n      if (typeof value === 'boolean') {\n        args.push(`--${key}`);\n      } else if (typeof value === 'number') {\n        args.push(`--${key}`, value.toFixed());\n      } else {\n        args.push(`--${key}`, value);\n      }\n    }\n  }\n  return args;\n}\n\nfunction getCommandOptions(commands: SharpCommandOptions[]): string[] {\n  const args: string[] = [];\n  for (const command of commands) {\n    if (command.operation === 'resize') {\n      const { operation, width, ...namedOptions } = command;\n      args.push(operation, width.toFixed(), ...getOptions(namedOptions));\n    } else {\n      const { operation, ...namedOptions } = command;\n      args.push(operation, ...getOptions(namedOptions));\n    }\n    args.push('--');\n  }\n  return args;\n}\n\nlet _sharpBin: string | null = null;\nlet _sharpInstance: any | null = null;\n\nasync function findSharpBinAsync(): Promise<string> {\n  if (_sharpBin) {\n    return _sharpBin;\n  }\n  try {\n    const sharpCliPackage = require('sharp-cli/package.json');\n    const libVipsVersion = require('sharp').versions.vips;\n    if (\n      sharpCliPackage &&\n      semver.satisfies(sharpCliPackage.version, SHARP_REQUIRED_VERSION) &&\n      typeof sharpCliPackage.bin.sharp === 'string' &&\n      typeof libVipsVersion === 'string'\n    ) {\n      _sharpBin = require.resolve(`sharp-cli/${sharpCliPackage.bin.sharp}`);\n      return _sharpBin;\n    }\n  } catch {\n    // fall back to global sharp-cli\n  }\n\n  let installedCliVersion;\n  try {\n    installedCliVersion = (await spawnAsync('sharp', ['--version'])).stdout.toString().trim();\n  } catch {\n    throw notFoundError(SHARP_REQUIRED_VERSION);\n  }\n\n  if (!semver.satisfies(installedCliVersion, SHARP_REQUIRED_VERSION)) {\n    showVersionMismatchWarning(SHARP_REQUIRED_VERSION, installedCliVersion);\n  }\n  _sharpBin = 'sharp';\n  return _sharpBin;\n}\n\n/**\n * Returns the instance of `sharp` installed by the global `sharp-cli` package.\n * This method will throw errors if the `sharp` instance cannot be found, these errors can be circumvented by ensuring `isAvailableAsync()` resolves to `true`.\n */\nexport async function findSharpInstanceAsync(): Promise<any | null> {\n  if (env.EXPO_IMAGE_UTILS_NO_SHARP) {\n    throw new Error(\n      'Global instance of sharp-cli cannot be retrieved because sharp-cli has been disabled with the environment variable `EXPO_IMAGE_UTILS_NO_SHARP`'\n    );\n  }\n  if (_sharpInstance) {\n    return _sharpInstance;\n  }\n  // Ensure sharp-cli version is correct\n  await findSharpBinAsync();\n\n  // Attempt to use local sharp package\n  try {\n    const sharp = require('sharp');\n    _sharpInstance = sharp;\n    return sharp;\n  } catch {}\n\n  // Attempt to resolve the sharp instance used by the global CLI\n  let sharpCliPath;\n  if (process.platform !== 'win32') {\n    try {\n      sharpCliPath = (await spawnAsync('which', ['sharp'])).stdout.toString().trim();\n    } catch {}\n  } else {\n    // On Windows systems, nested dependencies aren't linked to the paths within `require.resolve.paths`.\n    // Yarn installs these modules in a different folder, let's add yarn to the other attempts.\n    // See: https://github.com/expo/expo-cli/issues/2708\n    let yarnGlobalPath = '';\n    try {\n      yarnGlobalPath = path.join(\n        (await spawnAsync('yarn', ['global', 'dir'])).stdout.toString().trim(),\n        'node_modules'\n      );\n    } catch {}\n    try {\n      sharpCliPath = require.resolve('sharp-cli/package.json', {\n        paths: (require.resolve.paths('sharp-cli') || []).concat(yarnGlobalPath),\n      });\n    } catch {}\n  }\n\n  // resolve sharp from the sharp-cli package\n  const sharpPath = resolveFrom.silent(sharpCliPath || '', 'sharp');\n\n  if (sharpPath) {\n    try {\n      // attempt to require the global sharp package\n      _sharpInstance = require(sharpPath);\n    } catch {}\n  }\n\n  if (!_sharpInstance) {\n    throw new Error(`Failed to find the instance of sharp used by the global sharp-cli package.`);\n  }\n\n  return _sharpInstance;\n}\n\nfunction notFoundError(requiredCliVersion: string): Error {\n  return new Error(\n    `This command requires version ${requiredCliVersion} of \\`sharp-cli\\`. \\n` +\n      `You can install it using \\`npm install -g sharp-cli@${requiredCliVersion}\\`. \\n` +\n      '\\n' +\n      'For prerequisites, see: https://sharp.dimens.io/en/stable/install/#prerequisites'\n  );\n}\n\nlet versionMismatchWarningShown = false;\n\nfunction showVersionMismatchWarning(requiredCliVersion: string, installedCliVersion: string) {\n  if (versionMismatchWarningShown) {\n    return;\n  }\n  console.warn(\n    `Warning: This command requires version ${requiredCliVersion} of \\`sharp-cli\\`. \\n` +\n      `Currently installed version: \"${installedCliVersion}\" \\n` +\n      `Required version: \"${requiredCliVersion}\" \\n` +\n      `You can install it using \\`npm install -g sharp-cli@${requiredCliVersion}\\`. \\n` +\n      '\\n' +\n      'For prerequisites, see: https://sharp.dimens.io/en/stable/install/#prerequisites'\n  );\n  versionMismatchWarningShown = true;\n}\n"]}