# AgriAI - Prototype Download
This zip contains a minimal working prototype for AgriAI:
- backend/: Node.js + Express server (run with `node server.js` after `npm install`)
- frontend/: Expo React Native app (run with `npm start` inside frontend; use Expo Go)

Quick start:
1. Backend:
   - cd backend
   - npm install
   - (optional) edit .env.example -> .env with MONGO_URI
   - node server.js
2. Frontend:
   - cd frontend
   - npm install
   - npm start
   - Open in Expo Go on your phone or emulator.

Notes:
- The detect and chat endpoints are demo placeholders. Replace with real ML/OpenAI keys to enable advanced features.
- For Android emulator use API_BASE = http://********:4000/api in frontend/src/services/api.js
- For a real device, set API_BASE to your machine IP like http://192.168.x.y:4000/api
